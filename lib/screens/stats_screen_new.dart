import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../theme/app_theme.dart';
import '../services/translation_service.dart';
import '../services/api_service.dart';
import '../models/tense_category.dart';
import '../models/language.dart';

class StatsScreen extends StatefulWidget {
  const StatsScreen({super.key});

  @override
  State<StatsScreen> createState() => _StatsScreenState();
}

class _StatsScreenState extends State<StatsScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final TranslationService _translationService = TranslationService();
  final ApiService _apiService = ApiService();

  List<UserStats> _userStats = [];
  bool _isLoading = true;
  String? _error;
  String? _selectedLanguage;

  @override
  void initState() {
    super.initState();
    _translationService.addListener(_onLanguageChanged);
    _loadStats();
  }

  @override
  void dispose() {
    _translationService.removeListener(_onLanguageChanged);
    super.dispose();
  }

  void _onLanguageChanged() {
    setState(() {
      // Rebuild when language changes
    });
  }

  Future<void> _loadStats() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      try {
        final response = await _apiService.get('/v1/stats');

        if (response.statusCode == 200) {
          final data = response.data;

          if (data is List) {
            setState(() {
              _userStats = data
                  .map((json) => UserStats.fromJson(json))
                  .toList();
              _isLoading = false;

              // İlk dili varsayılan olarak seç
              if (_userStats.isNotEmpty && _selectedLanguage == null) {
                _selectedLanguage = _userStats.first.language;
              }
            });
          } else {
            setState(() {
              _userStats = [];
              _isLoading = false;
            });
          }
        } else if (response.statusCode == 404) {
          setState(() {
            _userStats = [];
            _isLoading = false;
            _error = 'insufficient_examples';
          });
        } else {
          setState(() {
            _error = 'Failed to load stats (Status: ${response.statusCode})';
            _isLoading = false;
          });
        }
      } catch (apiError) {
        // Mock data for development
        setState(() {
          _userStats = _getMockStats();
          _isLoading = false;

          if (_userStats.isNotEmpty && _selectedLanguage == null) {
            _selectedLanguage = _userStats.first.language;
          }
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Unexpected error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  List<UserStats> _getMockStats() {
    return [
      UserStats(
        language: 'English',
        totalExampleCount: '150',
        completedTranslations: '120',
        averageScore: '85.5',
        tenseStats: [
          TenseStats(
            tense: 0,
            totalTranslationCount: '40',
            averageScore: '90.0',
          ),
          TenseStats(
            tense: 1,
            totalTranslationCount: '35',
            averageScore: '82.5',
          ),
          TenseStats(
            tense: 2,
            totalTranslationCount: '30',
            averageScore: '88.0',
          ),
          TenseStats(
            tense: 3,
            totalTranslationCount: '15',
            averageScore: '75.0',
          ),
        ],
      ),
      UserStats(
        language: 'Spanish',
        totalExampleCount: '80',
        completedTranslations: '60',
        averageScore: '76.2',
        tenseStats: [
          TenseStats(
            tense: 0,
            totalTranslationCount: '20',
            averageScore: '85.0',
          ),
          TenseStats(
            tense: 1,
            totalTranslationCount: '18',
            averageScore: '72.5',
          ),
          TenseStats(
            tense: 2,
            totalTranslationCount: '15',
            averageScore: '80.0',
          ),
          TenseStats(
            tense: 3,
            totalTranslationCount: '7',
            averageScore: '65.0',
          ),
        ],
      ),
      UserStats(
        language: 'Turkish',
        totalExampleCount: '95',
        completedTranslations: '78',
        averageScore: '82.3',
        tenseStats: [
          TenseStats(
            tense: 0,
            totalTranslationCount: '25',
            averageScore: '88.0',
          ),
          TenseStats(
            tense: 1,
            totalTranslationCount: '22',
            averageScore: '79.5',
          ),
          TenseStats(
            tense: 2,
            totalTranslationCount: '20',
            averageScore: '85.0',
          ),
          TenseStats(
            tense: 3,
            totalTranslationCount: '11',
            averageScore: '70.0',
          ),
        ],
      ),
    ];
  }

  UserStats? get _selectedStats {
    if (_selectedLanguage == null) return null;
    try {
      return _userStats.firstWhere(
        (stats) => stats.language == _selectedLanguage,
      );
    } catch (e) {
      return null;
    }
  }

  String _getTenseName(int tenseId) {
    try {
      final tense = TenseCategory.getTenseById(tenseId);
      return _translationService.translate(tense.nameKey);
    } catch (e) {
      // Fallback names
      switch (tenseId) {
        case 0:
          return _translationService.translate('present_short');
        case 1:
          return _translationService.translate('past_short');
        case 2:
          return _translationService.translate('future_short');
        case 3:
          return _translationService.translate('perfect_short');
        default:
          return 'Tense $tenseId';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(
          _translationService.translate('stats'),
          style: const TextStyle(
            color: AppColors.textPrimary,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded, color: AppColors.primary),
            onPressed: _loadStats,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_error == 'insufficient_examples') {
      return _buildInsufficientDataState();
    }

    if (_error != null) {
      return _buildErrorState();
    }

    if (_userStats.isEmpty) {
      return _buildNoDataState();
    }

    return _buildStatsContent();
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          SizedBox(height: 16),
          Text(
            'Loading statistics...',
            style: TextStyle(color: AppColors.textSecondary, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildInsufficientDataState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.analytics_outlined,
                size: 60,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _translationService.translate('no_stats_yet'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              _translationService.translate('analysis_requirement'),
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to home screen - assuming main screen handles this
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                _translationService.translate('start_practicing'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 80, color: Colors.red),
            const SizedBox(height: 24),
            Text(
              _translationService.translate('error'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _error ?? 'Unknown error',
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _loadStats,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                _translationService.translate('try_again'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.bar_chart_outlined,
                size: 60,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _translationService.translate('no_stats_yet'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _translationService.translate('start_practicing_to_see_stats'),
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsContent() {
    final selectedStats = _selectedStats;

    return Column(
      children: [
        // Language selector
        _buildLanguageSelector(),

        // Main content
        Expanded(
          child: selectedStats == null
              ? _buildNoDataState()
              : SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 20),

                      // Overview cards
                      _buildOverviewCards(selectedStats),

                      const SizedBox(height: 32),

                      // Progress chart
                      _buildProgressChart(selectedStats),

                      const SizedBox(height: 32),

                      // Tense performance
                      _buildTensePerformance(selectedStats),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 0, 20, 0),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.language, color: AppColors.primary, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _translationService.translate('languages'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          GestureDetector(
            onTap: _showLanguageSelectionModal,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _selectedLanguage ?? 'Select Language',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.primary,
                    size: 18,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showLanguageSelectionModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                _translationService.translate('language_select'),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ),

            // Language list
            ...(_userStats.map(
              (stats) => ListTile(
                leading: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.language,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                title: Text(
                  stats.language,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                trailing: _selectedLanguage == stats.language
                    ? const Icon(Icons.check_circle, color: AppColors.primary)
                    : null,
                onTap: () {
                  setState(() {
                    _selectedLanguage = stats.language;
                  });
                  Navigator.pop(context);
                },
              ),
            )),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewCards(UserStats stats) {
    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            title: _translationService.translate('total_examples'),
            value: stats.totalExampleCount,
            icon: Icons.assignment_outlined,
            color: const Color(0xFF4F46E5),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMetricCard(
            title: _translationService.translate('completed'),
            value: stats.completedTranslations,
            icon: Icons.check_circle_outline,
            color: const Color(0xFF059669),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildMetricCard(
            title: _translationService.translate('average_score'),
            value: '${double.parse(stats.averageScore).toStringAsFixed(1)}%',
            icon: Icons.star_outline,
            color: const Color(0xFFD97706),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressChart(UserStats stats) {
    final completed = double.parse(stats.completedTranslations);
    final total = double.parse(stats.totalExampleCount);
    final progress = total > 0 ? (completed / total) * 100 : 0.0;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _translationService.translate('completion_progress'),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 20),

          // Circular progress
          Center(
            child: SizedBox(
              width: 160,
              height: 160,
              child: Stack(
                children: [
                  // Background circle
                  SizedBox(
                    width: 160,
                    height: 160,
                    child: CircularProgressIndicator(
                      value: 1.0,
                      strokeWidth: 12,
                      backgroundColor: AppColors.surface.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.surface.withOpacity(0.3),
                      ),
                    ),
                  ),
                  // Progress circle
                  SizedBox(
                    width: 160,
                    height: 160,
                    child: CircularProgressIndicator(
                      value: progress / 100,
                      strokeWidth: 12,
                      backgroundColor: Colors.transparent,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        AppColors.primary,
                      ),
                    ),
                  ),
                  // Center text
                  Positioned.fill(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '${progress.toStringAsFixed(1)}%',
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          Text(
                            _translationService.translate('completed'),
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Progress details
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Column(
                children: [
                  Text(
                    stats.completedTranslations,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  Text(
                    _translationService.translate('completed'),
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              Container(width: 1, height: 40, color: AppColors.surface),
              Column(
                children: [
                  Text(
                    (total - completed).toStringAsFixed(0),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    'Remaining',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTensePerformance(UserStats stats) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _translationService.translate('tense_performance'),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 20),

          ...stats.tenseStats.map((tenseStats) => _buildTenseItem(tenseStats)),
        ],
      ),
    );
  }

  Widget _buildTenseItem(TenseStats tenseStats) {
    final score = double.parse(tenseStats.averageScore);
    final progress = score / 100;

    Color progressColor;
    if (score >= 80) {
      progressColor = const Color(0xFF059669); // Green
    } else if (score >= 60) {
      progressColor = const Color(0xFFD97706); // Orange
    } else {
      progressColor = const Color(0xFFDC2626); // Red
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  _getTenseName(tenseStats.tense),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              Text(
                '${tenseStats.totalTranslationCount} translations',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                '${score.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: progressColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: AppColors.surface.withOpacity(0.3),
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              minHeight: 8,
            ),
          ),
        ],
      ),
    );
  }
}

// Data models
class UserStats {
  final String language;
  final String totalExampleCount;
  final String completedTranslations;
  final String averageScore;
  final List<TenseStats> tenseStats;

  UserStats({
    required this.language,
    required this.totalExampleCount,
    required this.completedTranslations,
    required this.averageScore,
    required this.tenseStats,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      language: json['language'] ?? '',
      totalExampleCount: json['totalExampleCount'] ?? '0',
      completedTranslations: json['completedTranslations'] ?? '0',
      averageScore: json['averageScore'] ?? '0',
      tenseStats: (json['tenseStats'] as List<dynamic>? ?? [])
          .map((tenseJson) => TenseStats.fromJson(tenseJson))
          .toList(),
    );
  }
}

class TenseStats {
  final int tense;
  final String totalTranslationCount;
  final String averageScore;

  TenseStats({
    required this.tense,
    required this.totalTranslationCount,
    required this.averageScore,
  });

  factory TenseStats.fromJson(Map<String, dynamic> json) {
    return TenseStats(
      tense: json['tense'] ?? 0,
      totalTranslationCount: json['totalTranslationCount'] ?? '0',
      averageScore: json['averageScore'] ?? '0',
    );
  }
}
