import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/category.dart';
import '../models/difficulty.dart';
import '../models/language.dart';
import '../models/tense_category.dart';
import '../widgets/category_selection_modal.dart';
import '../widgets/difficulty_selection_modal.dart';
import '../widgets/tense_selection_modal.dart';
import '../widgets/translation_result_modal.dart';
import '../widgets/error_screen.dart';
import '../services/translation_service.dart';
import '../services/translation_api_service.dart';
import '../services/initialization_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  final TextEditingController _targetTextController = TextEditingController();
  bool _isLoading = false;
  bool _isLoadingExample = false;
  bool _hasError = false;
  String _selectedCategoryId = 'everyday';
  String _selectedDifficultyId = 'easy';
  List<TenseCategory> _selectedTenses = []; // Empty by default (all tenses)
  String _exampleText = '';
  String? _currentExampleId; // Store the ID from getExample API
  String _displayedText = '';
  String _fullText = '';
  bool _showCursor = false;
  final TranslationService _translationService = TranslationService();
  final TranslationApiService _translationApiService = TranslationApiService();
  final InitializationService _initializationService = InitializationService();

  late AnimationController _fadeController;
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _bounceController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();
    _translationService.addListener(
      _onLanguageChanged,
    ); // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _pulseAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );
    _bounceAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _bounceController, curve: Curves.elasticOut),
    );

    // Auto-load example text when app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExampleText();
    });
  }

  @override
  void dispose() {
    _targetTextController.dispose();
    _translationService.removeListener(_onLanguageChanged);
    _fadeController.dispose();
    _pulseController.dispose();
    _rotationController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  void _onLanguageChanged() {
    setState(() {
      // Clear all states when language changes
      _exampleText = '';
      _currentExampleId = null; // Clear the ID when language changes
      _displayedText = '';
      _fullText = '';
      _showCursor = false;
      _targetTextController.clear();
      _isLoading = false;
      _isLoadingExample = false;
      _hasError = false; // Clear error state
    });

    // Reload example text with new language
    _loadExampleText();
  }

  // Type writer effect for displaying text
  Future<void> _typeWriterEffect(String text) async {
    if (!mounted) return;

    _displayedText = '';
    _fullText = text;
    _showCursor = true;

    for (int i = 0; i <= text.length; i++) {
      if (!mounted) return;

      setState(() {
        _displayedText = text.substring(0, i);
      });

      // Variable speed based on character type
      int delay = 30; // Base delay
      if (i < text.length) {
        final char = text[i];
        if (char == ' ') {
          delay = 50; // Slower for spaces
        } else if (char == '.' || char == ',' || char == '!' || char == '?') {
          delay = 120; // Much slower for punctuation
        } else if (char == '\n') {
          delay = 80; // Slower for line breaks
        }
      }

      await Future.delayed(Duration(milliseconds: delay));
    }

    // Hide cursor after typing is complete
    if (mounted) {
      setState(() {
        _showCursor = false;
      });
    }
  }

  Future<void> _loadExampleText() async {
    if (_isLoadingExample) return; // Prevent multiple simultaneous requests

    // Play bounce animation
    _bounceController.forward().then((_) => _bounceController.reverse());

    // Reset animations
    _fadeController.reset();
    _rotationController.repeat();

    setState(() {
      _isLoadingExample = true;
      _displayedText = '';
      _showCursor = false;
      _hasError = false; // Clear previous error
    });

    try {
      final exampleResult = await _translationApiService.getExample(
        language: _translationService.nativeLanguageCode,
        category: _selectedCategoryId,
        difficulty: _selectedDifficultyId,
        tenseCategories: _selectedTenses.isNotEmpty ? _selectedTenses : null,
      );

      final exampleText = exampleResult['text'] as String;
      final exampleId = exampleResult['id'] as String?;

      if (!mounted) return;

      setState(() {
        _exampleText = exampleText;
        _currentExampleId = exampleId; // Store the ID for later use
        _isLoadingExample = false;
        _hasError = false;
      });

      // Stop rotation and start fade in animation
      _rotationController.stop();
      _fadeController.forward();

      // Start typing animation
      await _typeWriterEffect(exampleText);
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _exampleText = '';
        _currentExampleId = null; // Clear ID on error
        _isLoadingExample = false;
        _hasError = true; // Set error state
      });

      // Stop rotation animation
      _rotationController.stop();
      _fadeController.forward();
    }
  }

  Future<void> _checkTranslation() async {
    if (_exampleText.trim().isEmpty ||
        _targetTextController.text.trim().isEmpty) {
      // Show a subtle hint animation
      _bounceController.forward().then((_) => _bounceController.reverse());
      return;
    }

    // Hide keyboard
    FocusScope.of(context).unfocus();

    setState(() {
      _isLoading = true;
    });

    try {
      // Get target and native language info
      final targetLanguage = Languages.getByCode(
        _translationService.targetLanguageCode,
      );
      final nativeLanguage = Languages.getByCode(
        _translationService.nativeLanguageCode,
      );

      // Call API to check translation
      final result = await _translationApiService.checkTranslation(
        userTranslation: _targetTextController.text.trim(),
        originalText: _exampleText,
        targetLanguage: targetLanguage.code, // or use targetLanguage.name
        nativeLanguage: nativeLanguage.code, // or use nativeLanguage.name
        remoteReferenceId: _currentExampleId, // Pass the ID from getExample
      );

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      if (result != null) {
        // Show result modal
        await TranslationResultModal.show(
          context,
          userTranslation: _targetTextController.text.trim(),
          suggestedTranslation:
              result['suggestedTranslation'] ?? 'No suggestion available',
          explanation:
              result['translateExplanations'] ?? 'No explanation available',
          score: result['score'] ?? 0,
        );
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    _translationService.translate('failed_translation_check'),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text('Error: ${e.toString()}'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  // Helper method to get category by ID from dynamic or static sources
  Category _getCategoryById(String id) {
    if (_initializationService.isInitialized) {
      return _initializationService.categories.firstWhere(
        (cat) => cat.id == id,
        orElse: () => Categories.getById(id),
      );
    } else {
      return Categories.getById(id);
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Keep alive mixin için gerekli

    final selectedCategory = _getCategoryById(_selectedCategoryId);
    final selectedDifficulty = Difficulties.getById(_selectedDifficultyId);

    return Scaffold(
      appBar: AppBar(
        title: Text(_translationService.translate('app_title')),
        centerTitle: true,
      ),
      body: _hasError
          ? ErrorScreen(
              title: _translationService.translate('connection_error'),
              message: _translationService.translate('connection_error_msg'),
              onRetry: _loadExampleText,
              isLoading: _isLoadingExample,
            )
          : Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const SizedBox(height: 12),

                        // Kategori seçimi
                        _buildCategorySelector(selectedCategory),

                        const SizedBox(height: 32),

                        // Başlık
                        Text(
                          _translationService.translate('translation_practice'),
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 8),

                        // Alt başlık
                        Text(
                          _translationService.translate('translation_subtitle'),
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 32),

                        // Example text (read-only) with difficulty selector and refresh
                        _buildExampleField(selectedDifficulty),

                        const SizedBox(height: 20),

                        // Hedef dil girişi
                        _buildInputField(
                          controller: _targetTextController,
                          label: _translationService.translate(
                            'target_language_label',
                          ),
                          hint: _translationService.translate(
                            'target_language_hint',
                          ),
                          maxLines: 3,
                        ),

                        const SizedBox(height: 32),

                        // Kontrol butonu
                        _buildCheckButton(),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildExampleField(Difficulty selectedDifficulty) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with difficulty selector
        Row(
          children: [
            Expanded(
              child: Text(
                _translationService.translate('native_language_label'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ),
            // Difficulty selector
            GestureDetector(
              onTap: () {
                DifficultySelectionModal.show(
                  context,
                  selectedDifficultyId: _selectedDifficultyId,
                  onDifficultySelected: (difficulty) {
                    setState(() {
                      _selectedDifficultyId = difficulty.id;
                    });
                    _loadExampleText();
                  },
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: selectedDifficulty.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: selectedDifficulty.secondaryColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      selectedDifficulty.icon,
                      color: selectedDifficulty.primaryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      selectedDifficulty.name,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: selectedDifficulty.primaryColor,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: selectedDifficulty.primaryColor,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Tense filter button
            GestureDetector(
              onTap: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) => TenseSelectionModal(
                    selectedTenses: _selectedTenses,
                    onTensesSelected: (tenses) {
                      setState(() {
                        _selectedTenses = tenses;
                      });
                      _loadExampleText(); // Reload with new tense filter
                    },
                  ),
                );
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: _selectedTenses.isNotEmpty
                      ? AppColors.primary.withOpacity(0.15)
                      : AppColors.surface.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(18),
                  border: _selectedTenses.isNotEmpty
                      ? Border.all(
                          color: AppColors.primary.withOpacity(0.3),
                          width: 1,
                        )
                      : null,
                ),
                child: Icon(
                  Icons.tune,
                  color: _selectedTenses.isNotEmpty
                      ? AppColors.primary
                      : AppColors.textSecondary,
                  size: 18,
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Refresh button
            GestureDetector(
              onTap: _isLoadingExample ? null : _loadExampleText,
              child: AnimatedBuilder(
                animation: _bounceAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _bounceAnimation.value,
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(18),
                      ),
                      child: _isLoadingExample
                          ? AnimatedBuilder(
                              animation: _rotationAnimation,
                              builder: (context, child) {
                                return Transform.rotate(
                                  angle:
                                      _rotationAnimation.value *
                                      6.28, // 2π for full rotation
                                  child: const Icon(
                                    Icons.refresh,
                                    color: AppColors.primary,
                                    size: 18,
                                  ),
                                );
                              },
                            )
                          : const Icon(
                              Icons.refresh,
                              color: AppColors.primary,
                              size: 18,
                            ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Example text container
        Container(
          decoration: BoxDecoration(
            color: AppColors.surface.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.surface, width: 2),
            boxShadow: [
              BoxShadow(
                color: AppColors.surface.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Container(
            width: double.infinity,
            constraints: const BoxConstraints(minHeight: 72),
            padding: const EdgeInsets.all(16),
            child: _isLoadingExample
                ? Center(
                    child: AnimatedBuilder(
                      animation: _rotationAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _rotationAnimation.value * 6.28,
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primary,
                                  AppColors.primary.withValues(alpha: 0.3),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: const Icon(
                              Icons.autorenew,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        );
                      },
                    ),
                  )
                : FadeTransition(
                    opacity: _fadeAnimation,
                    child: AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: _displayedText.isEmpty
                                    ? _translationService.translate(
                                        'native_language_hint',
                                      )
                                    : _displayedText,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: _displayedText.isEmpty
                                      ? AppColors.textSecondary.withValues(
                                          alpha: 0.7,
                                        )
                                      : AppColors.textPrimary,
                                ),
                              ),
                              // Blinking cursor
                              if ((_displayedText.isNotEmpty &&
                                      _displayedText != _fullText) ||
                                  _showCursor)
                                TextSpan(
                                  text: '|',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppColors.primary.withValues(
                                      alpha: _pulseAnimation.value,
                                    ),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.surface, width: 2),
            boxShadow: [
              BoxShadow(
                color: AppColors.surface.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: controller,
            maxLines: maxLines,
            style: const TextStyle(fontSize: 16, color: AppColors.textPrimary),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: AppColors.textSecondary.withOpacity(0.7),
                fontSize: 15,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCheckButton() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _checkTranslation,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.background,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32),
        ),
        child: _isLoading
            ? AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale:
                        0.8 +
                        (_pulseAnimation.value *
                            0.4), // Scale between 0.8 and 1.2
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        gradient: LinearGradient(
                          colors: [
                            AppColors.background,
                            AppColors.background.withOpacity(0.7),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.background.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.check_circle_outline,
                        color: AppColors.primary,
                        size: 24,
                      ),
                    ),
                  );
                },
              )
            : Text(
                _translationService.translate('check_button'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildCategorySelector(Category selectedCategory) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.surface, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.surface.withOpacity(0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Kategori ikonu
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: selectedCategory.secondaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              selectedCategory.icon,
              color: selectedCategory.primaryColor,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // Kategori bilgileri
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _translationService.translate('category'),
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  selectedCategory.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),

          // Ayarlar butonu
          GestureDetector(
            onTap: () {
              CategorySelectionModal.show(
                context,
                selectedCategoryId: _selectedCategoryId,
                onCategorySelected: (category) {
                  setState(() {
                    _selectedCategoryId = category.id;
                  });
                  _loadExampleText(); // Reload example when category changes
                },
              );
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.surface.withOpacity(0.5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.settings,
                color: AppColors.textSecondary,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
