import 'package:flutter/material.dart';
import 'package:lingoflow/screens/stats_screen.dart';
import '../theme/app_theme.dart';
import '../theme/app_icons.dart';
import '../services/translation_service.dart';
import 'home_screen.dart';
import 'profile_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  final TranslationService _translationService = TranslationService();

  final List<Widget> _pages = [
    const HomeScreen(),
    const StatsScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Listen to language changes to rebuild the navigation
    _translationService.addListener(_onLanguageChanged);
  }

  @override
  void dispose() {
    _translationService.removeListener(_onLanguageChanged);
    super.dispose();
  }

  void _onLanguageChanged() {
    setState(() {
      // Rebuild when language changes
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: _pages),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: AppColors.background,
          border: Border(top: BorderSide(color: AppColors.surface, width: 1)),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          items: [
            BottomNavigationBarItem(
              icon: const Icon(AppIcons.homeOutlined),
              activeIcon: const Icon(AppIcons.home),
              label: _translationService.translate('home'),
            ),
            BottomNavigationBarItem(
              icon: const Icon(AppIcons.progressOutlined),
              activeIcon: const Icon(AppIcons.progress),
              label: _translationService.translate('stats'),
            ),
            BottomNavigationBarItem(
              icon: const Icon(AppIcons.profileOutlined),
              activeIcon: const Icon(AppIcons.profile),
              label: _translationService.translate('profile'),
            ),
          ],
        ),
      ),
    );
  }
}
