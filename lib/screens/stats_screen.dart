import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../theme/app_theme.dart';
import '../services/translation_service.dart';
import '../services/api_service.dart';
import '../models/tense_category.dart';
import '../widgets/mediterranean_diet_view.dart';

class StatsScreen extends StatefulWidget {
  const StatsScreen({super.key});

  @override
  State<StatsScreen> createState() => _StatsScreenState();
}

class _StatsScreenState extends State<StatsScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  @override
  bool get wantKeepAlive => true;

  final TranslationService _translationService = TranslationService();
  final ApiService _apiService = ApiService();

  List<UserStats> _userStats = [];
  bool _isLoading = true;
  String? _error;
  String? _selectedLanguage;

  late AnimationController _dietAnimationController;
  late Animation<double> _dietAnimation;

  @override
  void initState() {
    super.initState();
    _translationService.addListener(_onLanguageChanged);
    _dietAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _dietAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _dietAnimationController, curve: Curves.easeOut),
    );
    _dietAnimationController.forward();
    _loadStats();
  }

  @override
  void dispose() {
    _translationService.removeListener(_onLanguageChanged);
    _dietAnimationController.dispose();
    super.dispose();
  }

  void _onLanguageChanged() {
    if (!mounted) return;
    setState(() {
      // Rebuild when language changes
    });
  }

  Future<void> _loadStats() async {
    try {
      if (!mounted) return;
      setState(() {
        _isLoading = true;
        _error = null;
      });

      try {
        final response = await _apiService.get('/v1/stats');

        if (response.statusCode == 200) {
          final data = response.data;

          if (data is List) {
            if (!mounted) return;
            setState(() {
              _userStats = data
                  .map((json) => UserStats.fromJson(json))
                  .toList();
              _isLoading = false;

              // İlk dili varsayılan olarak seç
              if (_userStats.isNotEmpty && _selectedLanguage == null) {
                _selectedLanguage = _userStats.first.language;
              }
            });
          } else {
            if (!mounted) return;
            setState(() {
              _userStats = [];
              _isLoading = false;
            });
          }
        } else if (response.statusCode == 404) {
          if (!mounted) return;
          setState(() {
            _userStats = [];
            _isLoading = false;
            _error = 'insufficient_examples';
          });
        } else {
          if (!mounted) return;
          setState(() {
            _error = 'Failed to load stats (Status: ${response.statusCode})';
            _isLoading = false;
          });
        }
      } catch (apiError) {
        if (!mounted) return;
        setState(() {
          _error = 'Failed to connect to server: ${apiError.toString()}';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _error = 'Unexpected error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  UserStats? get _selectedStats {
    if (_selectedLanguage == null) return null;
    try {
      return _userStats.firstWhere(
        (stats) => stats.language == _selectedLanguage,
      );
    } catch (e) {
      return null;
    }
  }

  String _getTenseName(int tenseId) {
    try {
      final tense = TenseCategory.getTenseById(tenseId);
      return _translationService.translate(tense.nameKey);
    } catch (e) {
      // Fallback names
      switch (tenseId) {
        case 0:
          return _translationService.translate('present_short');
        case 1:
          return _translationService.translate('past_short');
        case 2:
          return _translationService.translate('future_short');
        case 3:
          return _translationService.translate('perfect_short');
        default:
          return 'Tense $tenseId';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Column(
          children: [
            // Modern App Bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.arrow_back_ios_new, size: 18),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _translationService.translate('stats'),
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF1A1A1A),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: _loadStats,
                      icon: const Icon(Icons.refresh_rounded, size: 18),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ),

            Expanded(child: _buildBody()),
          ],
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_error == 'insufficient_examples') {
      return _buildInsufficientDataState();
    }

    if (_error != null) {
      return _buildErrorState();
    }

    if (_userStats.isEmpty) {
      return _buildNoDataState();
    }

    return _buildStatsContent();
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          SizedBox(height: 16),
          Text(
            'Loading statistics...',
            style: TextStyle(color: AppColors.textSecondary, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildInsufficientDataState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.analytics_outlined,
                size: 60,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _translationService.translate('no_stats_yet'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              _translationService.translate('analysis_requirement'),
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to home screen - assuming main screen handles this
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                _translationService.translate('start_practicing'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 80, color: Colors.red),
            const SizedBox(height: 24),
            Text(
              _translationService.translate('error'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _error ?? 'Unknown error',
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _loadStats,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                _translationService.translate('try_again'),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoDataState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.bar_chart_outlined,
                size: 60,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _translationService.translate('no_stats_yet'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _translationService.translate('start_practicing_to_see_stats'),
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsContent() {
    final selectedStats = _selectedStats;

    return CustomScrollView(
      slivers: [
        // Mediterranean Diet View at the top
        SliverToBoxAdapter(
          child: MediterranesnDietView(
            animationController: _dietAnimationController,
            animation: _dietAnimation,
          ),
        ),

        // Language selector
        SliverToBoxAdapter(child: _buildModernLanguageSelector()),

        // Content
        if (selectedStats == null)
          SliverFillRemaining(child: _buildNoDataState())
        else
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  _buildModernOverviewCards(selectedStats),
                  const SizedBox(height: 32),
                  _buildModernProgressCard(selectedStats),
                  const SizedBox(height: 32),
                  _buildModernTensePerformance(selectedStats),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildModernLanguageSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: GestureDetector(
        onTap: _showLanguageSelectionModal,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.language_rounded,
                  color: Color(0xFF6366F1),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Language',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _selectedLanguage ?? 'Select Language',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.chevron_right_rounded,
                color: Color(0xFF9CA3AF),
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernOverviewCards(UserStats stats) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildModernMetricCard(
                title: 'Total',
                value: stats.totalExampleCount,
                icon: Icons.auto_stories_rounded,
                color: const Color(0xFF3B82F6),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernMetricCard(
                title: 'Completed',
                value: stats.completedTranslations,
                icon: Icons.check_circle_rounded,
                color: const Color(0xFF10B981),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildModernMetricCard(
          title: 'Average Score',
          value: '${double.parse(stats.averageScore).toStringAsFixed(1)}%',
          icon: Icons.trending_up_rounded,
          color: const Color(0xFFF59E0B),
          isWide: true,
        ),
      ],
    );
  }

  Widget _buildModernMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    bool isWide = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: isWide
          ? Row(
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(icon, color: color, size: 28),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF6B7280),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        value,
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildModernProgressCard(UserStats stats) {
    final completed = double.parse(stats.completedTranslations);
    final total = double.parse(stats.totalExampleCount);
    final progress = total > 0 ? (completed / total) : 0.0;

    return Container(
      padding: const EdgeInsets.all(28),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Progress',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 24),

          // Progress Chart using fl_chart
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sectionsSpace: 4,
                centerSpaceRadius: 80,
                sections: [
                  PieChartSectionData(
                    color: const Color(0xFF6366F1),
                    value: progress * 100,
                    title: '${(progress * 100).toStringAsFixed(1)}%',
                    radius: 40,
                    titleStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  PieChartSectionData(
                    color: const Color(0xFFF3F4F6),
                    value: (1 - progress) * 100,
                    title: '',
                    radius: 40,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: _buildProgressStat(
                  'Completed',
                  stats.completedTranslations,
                  const Color(0xFF6366F1),
                ),
              ),
              Container(width: 1, height: 40, color: const Color(0xFFF3F4F6)),
              Expanded(
                child: _buildProgressStat(
                  'Remaining',
                  (total - completed).toStringAsFixed(0),
                  const Color(0xFF9CA3AF),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF6B7280),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildModernTensePerformance(UserStats stats) {
    return Container(
      padding: const EdgeInsets.all(28),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Tense Performance',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 24),
          ...stats.tenseStats.map(
            (tenseStats) => _buildModernTenseItem(tenseStats),
          ),
        ],
      ),
    );
  }

  Widget _buildModernTenseItem(TenseStats tenseStats) {
    final score = double.parse(tenseStats.averageScore);
    final progress = score / 100;

    Color progressColor;
    if (score >= 80) {
      progressColor = const Color(0xFF10B981);
    } else if (score >= 60) {
      progressColor = const Color(0xFFF59E0B);
    } else {
      progressColor = const Color(0xFFEF4444);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  _getTenseName(tenseStats.tense),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ),
              Text(
                '${score.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: progressColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: const Color(0xFFF3F4F6),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progress,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: progressColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showLanguageSelectionModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(0xFFE5E7EB),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(24),
                child: Text(
                  'Select Language',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ),

              // Language list
              ...(_userStats.map(
                (stats) => Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 4,
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    leading: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: const Color(0xFF6366F1).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.language_rounded,
                        color: Color(0xFF6366F1),
                        size: 24,
                      ),
                    ),
                    title: Text(
                      stats.language,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    trailing: _selectedLanguage == stats.language
                        ? const Icon(
                            Icons.check_circle_rounded,
                            color: Color(0xFF6366F1),
                            size: 24,
                          )
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedLanguage = stats.language;
                      });
                      Navigator.pop(context);
                    },
                  ),
                ),
              )),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}

// Data models
class UserStats {
  final String language;
  final String totalExampleCount;
  final String completedTranslations;
  final String averageScore;
  final List<TenseStats> tenseStats;

  UserStats({
    required this.language,
    required this.totalExampleCount,
    required this.completedTranslations,
    required this.averageScore,
    required this.tenseStats,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      language: json['language'] ?? '',
      totalExampleCount: json['totalExampleCount'] ?? '0',
      completedTranslations: json['completedTranslations'] ?? '0',
      averageScore: json['averageScore'] ?? '0',
      tenseStats: (json['tenseStats'] as List<dynamic>? ?? [])
          .map((tenseJson) => TenseStats.fromJson(tenseJson))
          .toList(),
    );
  }
}

class TenseStats {
  final int tense;
  final String totalTranslationCount;
  final String averageScore;

  TenseStats({
    required this.tense,
    required this.totalTranslationCount,
    required this.averageScore,
  });

  factory TenseStats.fromJson(Map<String, dynamic> json) {
    return TenseStats(
      tense: json['tense'] ?? 0,
      totalTranslationCount: json['totalTranslationCount'] ?? '0',
      averageScore: json['averageScore'] ?? '0',
    );
  }
}
